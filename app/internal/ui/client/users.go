package client

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/information-sharing-networks/signalsd/app/internal/ui/types"
)

// GetUsers returns a list of users for use in a dropdown component
func (c *Client) GetUsers(accessToken string) ([]types.UserOption, error) {

	url := fmt.Sprintf("%s/api/admin/users", c.baseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, NewClientInternalError(err, "creating service account request")
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))

	res, err := c.httpClient.Do(req)
	if err != nil {
		return nil, NewClientConnectionError(err)
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		return nil, NewClientApiError(res)
	}

	var users []types.UserOption
	if err := json.NewDecoder(res.Body).Decode(&users); err != nil {
		return nil, NewClientInternalError(err, "decoding service accounts response")
	}

	return users, nil

}
