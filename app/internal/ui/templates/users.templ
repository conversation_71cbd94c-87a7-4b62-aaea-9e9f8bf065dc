package templates


import (
)

templ ManageUsersPage() {
	@BaseLayout("Manager web users") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">Manage Users</h1>

			<!-- Create New Service Account Section -->
			<div class="card mb-6">
				<div class="card-body">
					<h3 class="card-title">Generate password reset link for a user</h3>
    		        <p class="text-muted mb-4">Select an existing user account to generate a password reset link.</p>
					<div class="form-group">
						<label for="user-dropdown" class="form-label">User </label>
						<select
							id="user-dropdown"
							hx-get="/ui-api/user-options"
							hx-trigger="load"
							hx-swap="outerHTML"
						>
							<!--web user dropdown will be loaded here -->
						</select>
					</div>

					<div id="generate-password-reset-button-container" class="form-group mt-4">
                        <button
                            id="generate-password-reset-btn"
                            hx-post="/ui-api/generate-password-reset-link-todo"
                            hx-target="#generate-password-reset-result"
                            hx-include="#user-dropdown"
                            class="btn "
                            disabled>
                                Generate Reset Link
                        </button>
					</div>

					<!-- Update button state when dropdown changes -->
					<div hx-get="/ui-api/generate-password-reset-button-state"
						hx-trigger="change from:#user-dropdown"
						hx-target="#generate-password-reset-button-container"
						hx-swap="innerHTML"
						style="display: none;">
					</div>

					<div id="generate-password-reset-result" class="mt-4"></div>
				</div>
			</div>

		</div>
        <!-- todo disable user account -->
	}
}



// reissue button template
templ GeneratePasswordResetButton(isEnabled bool) {
	<button
		id="generate-password-reset-btn"
		hx-post="/ui-api/generate-password-reset-link-todo"
		hx-target="#generate-password-reset-result"
		hx-include="#user-dropdown"
		class="btn "
		disabled?={ !isEnabled }
	>
		Generate Reset Link
	</button>
}
