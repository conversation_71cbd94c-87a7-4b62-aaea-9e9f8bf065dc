package handlers

import (
	"log/slog"
	"net/http"

	"github.com/information-sharing-networks/signalsd/app/internal/logger"
	"github.com/information-sharing-networks/signalsd/app/internal/ui/templates"
)

func (h *HandlerService) ManageUsersPage(w http.ResponseWriter, r *http.Request) {
	reqLogger := logger.ContextRequestLogger(r.Context())

	if err := templates.ManageUsersPage().Render(r.Context(), w); err != nil {
		reqLogger.Error("Failed to render manage users page", slog.String("error", err.Error()))
	}
}

// ReissueButtonState returns the reissue button in the correct enabled/disabled state
func (h *HandlerService) GeneratePasswordResetButtonState(w http.ResponseWriter, r *http.Request) {
	reqLogger := logger.ContextRequestLogger(r.Context())

	reqLogger.Debug("!!!!generate password reset button state")
	user := r.<PERSON>ue("user-dropdown")
	isEnabled := user != ""

	if err := templates.GeneratePasswordResetButton(isEnabled).<PERSON><PERSON>(r.Context(), w); err != nil {
		reqLogger.Error("Failed to render generate password reset button", slog.String("error", err.Error()))
	}
}
